<!-- 特殊情况说明 -->
<view class="special-note-section" wx:if="{{specialNoteData}}">
  <view class="section-title">特殊情况说明</view>
  <view class="special-note-content">
    <view class="note-info">
      <view class="note-header">
        <text class="note-employee">记录员工：{{specialNoteData.employee.name}}</text>
        <text class="note-time">{{specialNoteData.createdAt}}</text>
      </view>

      <!-- 文字内容 -->
      <view class="note-text" wx:if="{{specialNoteData.content}}">
        <text class="content-text">{{specialNoteData.content}}</text>
      </view>

      <!-- 图片内容 -->
      <view class="note-photos" wx:if="{{specialNoteData.photos && specialNoteData.photos.length > 0}}">
        <view class="photos-grid">
          <image
            wx:for="{{specialNoteData.photos}}"
            wx:key="index"
            src="{{item}}"
            class="note-photo"
            mode="aspectFill"
            bindtap="onPreviewPhoto"
            data-url="{{item}}"
            data-urls="{{specialNoteData.photos}}"
          ></image>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="note-actions" wx:if="{{orderDetail.status === '服务中'}}">
      <view class="note-action-btn" bindtap="onShowSpecialNote">
        编辑说明
      </view>
    </view>

    <!-- 查看按钮（已完成状态） -->
    <view class="note-actions" wx:if="{{orderDetail.status === '已完成' || orderDetail.status === '已评价'}}">
      <view class="note-action-btn view-btn" bindtap="onShowSpecialNote">
        查看详情
      </view>
    </view>
  </view>
</view>

<!-- 特殊情况说明入口（无数据时显示） -->
<view class="special-note-entry" wx:if="{{!specialNoteData && (orderDetail.status === '服务中')}}">
  <view class="section-title">特殊情况说明</view>
  <view class="entry-content" bindtap="onShowSpecialNote">
    <view class="entry-icon">📝</view>
    <text class="entry-text">点击添加特殊情况说明</text>
    <view class="entry-arrow">></view>
  </view>
</view>
