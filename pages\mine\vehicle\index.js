// pages/mine/vehicle/index.js
import vehicleApi from '../../../api/modules/vehicle.js';
import dictionaryApi from '../../../api/modules/dictionary.js';
import Session from '../../../common/Session.js';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    vehicleInfo: null,
    loading: true,
    editing: false,
    formData: {
      mileage: '',
      appearance: '',
      insuranceExpiry: '',
      licenseExpiry: '',
      supplies: ''
    },
    originalFormData: {},
    minDate: '',
    maxDate: '',
    vehicleTypeDictionary: [] // 车辆类型字典
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.initUserInfo();
    this.initDateRange();
    this.loadDictionary();
    this.loadVehicleInfo();
  },

  // 初始化日期范围
  initDateRange() {
    const today = new Date();
    const maxDate = new Date();
    maxDate.setFullYear(today.getFullYear() + 10);

    this.setData({
      minDate: this.formatDate(today),
      maxDate: this.formatDate(maxDate)
    });
  },

  // 加载字典数据
  async loadDictionary() {
    try {
      const vehicleTypeDictionary = await dictionaryApi.list('vehicle_type');
      this.setData({
        vehicleTypeDictionary: vehicleTypeDictionary || []
      });
      console.log('车辆类型字典加载成功:', vehicleTypeDictionary);
    } catch (error) {
      console.error('加载字典数据失败:', error);
      // 静默失败，不影响主要功能
    }
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = Session.getUser();
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    this.setData({ userInfo });
  },

  // 加载车辆信息
  async loadVehicleInfo() {
    const { userInfo } = this.data;
    
    try {
      this.setData({ loading: true });
      const vehicleInfo = await vehicleApi.getEmployeeVehicle(userInfo.id);
      
      if (vehicleInfo) {
        // 添加状态类名
        vehicleInfo.statusClass = this.getStatusClass(vehicleInfo.status);

        // 添加车辆类型中文名称
        vehicleInfo.vehicleTypeName = this.getVehicleTypeName(vehicleInfo.vehicleType);

        // 格式化日期显示
        const formData = {
          mileage: vehicleInfo.mileage ? vehicleInfo.mileage.toString() : '',
          appearance: vehicleInfo.appearance || '',
          insuranceExpiry: vehicleInfo.insuranceExpiry ? this.formatDate(vehicleInfo.insuranceExpiry) : '',
          licenseExpiry: vehicleInfo.licenseExpiry ? this.formatDate(vehicleInfo.licenseExpiry) : '',
          supplies: vehicleInfo.supplies || ''
        };

        this.setData({
          vehicleInfo,
          formData,
          originalFormData: { ...formData }
        });
      } else {
        wx.showToast({
          title: '未分配车辆',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('加载车辆信息失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 获取状态对应的CSS类名
  getStatusClass(status) {
    const statusMap = {
      '空闲': 'idle',
      '使用中': 'in-use',
      '维修中': 'maintenance',
      '停用': 'disabled'
    };
    return statusMap[status] || 'idle';
  },

  // 获取车辆类型中文名称
  getVehicleTypeName(vehicleType) {
    const { vehicleTypeDictionary } = this.data;
    if (!vehicleType || !vehicleTypeDictionary || vehicleTypeDictionary.length === 0) {
      return vehicleType || '未知类型';
    }

    const typeItem = vehicleTypeDictionary.find(item => item.value === vehicleType);
    return typeItem ? typeItem.label : vehicleType;
  },

  // 检查表单是否有变更
  hasFormChanges(currentData, originalData) {
    const fields = ['mileage', 'appearance', 'insuranceExpiry', 'licenseExpiry', 'supplies'];

    for (let field of fields) {
      const current = currentData[field] || '';
      const original = originalData[field] || '';
      if (current !== original) {
        return true;
      }
    }

    return false;
  },

  // 切换编辑模式
  toggleEdit(e) {
    console.log('切换编辑模式', e);

    // 阻止事件冒泡
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }

    const { editing } = this.data;
    if (editing) {
      // 取消编辑，恢复原始数据
      this.setData({
        editing: false,
        formData: { ...this.data.originalFormData }
      });
    } else {
      // 开始编辑
      this.setData({ editing: true });
    }
  },

  // 表单输入处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 保险到期日期选择
  onInsuranceDateChange(e) {
    const selectedDate = e.detail.value;
    this.setData({
      'formData.insuranceExpiry': selectedDate
    });
  },

  // 行驶证到期日期选择
  onLicenseDateChange(e) {
    const selectedDate = e.detail.value;
    this.setData({
      'formData.licenseExpiry': selectedDate
    });
  },

  // 验证表单数据
  validateForm() {
    const { formData } = this.data;
    
    // 里程数验证
    if (formData.mileage && (isNaN(formData.mileage) || parseFloat(formData.mileage) < 0 || parseFloat(formData.mileage) > 999999)) {
      wx.showToast({
        title: '里程数格式不正确',
        icon: 'none'
      });
      return false;
    }
    
    // 外观描述长度验证
    if (formData.appearance && formData.appearance.length > 1000) {
      wx.showToast({
        title: '外观描述不能超过1000字符',
        icon: 'none'
      });
      return false;
    }
    
    // 物资清单长度验证
    if (formData.supplies && formData.supplies.length > 2000) {
      wx.showToast({
        title: '物资清单不能超过2000字符',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  },

  // 保存车辆信息
  async saveVehicleInfo(e) {
    console.log('保存车辆信息开始', e);

    // 阻止事件冒泡
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }

    // 先显示一个确认提示，确保点击事件正常工作
    wx.showToast({
      title: '正在保存...',
      icon: 'loading',
      duration: 1000
    });

    if (!this.validateForm()) {
      console.log('表单验证失败');
      return;
    }

    const { userInfo, vehicleInfo, formData, originalFormData } = this.data;
    console.log('当前表单数据:', formData);
    console.log('原始表单数据:', originalFormData);

    try {
      // 检查是否有数据变更
      const hasChanges = this.hasFormChanges(formData, originalFormData);
      console.log('是否有数据变更:', hasChanges);

      if (!hasChanges) {
        wx.showToast({
          title: '没有数据变更',
          icon: 'none'
        });
        this.setData({ editing: false });
        return;
      }

      // 准备提交数据
      const submitData = {};

      if (formData.mileage && formData.mileage !== originalFormData.mileage) {
        submitData.mileage = parseFloat(formData.mileage);
      }
      if (formData.appearance && formData.appearance !== originalFormData.appearance) {
        submitData.appearance = formData.appearance;
      }
      if (formData.insuranceExpiry && formData.insuranceExpiry !== originalFormData.insuranceExpiry) {
        submitData.insuranceExpiry = formData.insuranceExpiry;
      }
      if (formData.licenseExpiry && formData.licenseExpiry !== originalFormData.licenseExpiry) {
        submitData.licenseExpiry = formData.licenseExpiry;
      }
      if (formData.supplies && formData.supplies !== originalFormData.supplies) {
        submitData.supplies = formData.supplies;
      }

      console.log('准备提交的数据:', submitData);
      console.log('车辆ID:', vehicleInfo.id, '员工ID:', userInfo.id);

      const result = await vehicleApi.updateVehicleInfo(vehicleInfo.id, userInfo.id, submitData);
      console.log('API调用结果:', result);

      if (result) {
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        });

        // 更新本地数据
        this.setData({
          editing: false,
          originalFormData: { ...formData }
        });

        // 重新加载车辆信息
        this.loadVehicleInfo();
      } else {
        wx.showToast({
          title: '更新失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('保存车辆信息失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadVehicleInfo().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {}
});
