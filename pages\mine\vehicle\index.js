// pages/mine/vehicle/index.js
import vehicleApi from '../../../api/modules/vehicle.js';
import Session from '../../../common/Session.js';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    vehicleInfo: null,
    loading: true,
    editing: false,
    formData: {
      mileage: '',
      appearance: '',
      insuranceExpiry: '',
      licenseExpiry: '',
      supplies: ''
    },
    originalFormData: {},
    minDate: '',
    maxDate: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initUserInfo();
    this.initDateRange();
    this.loadVehicleInfo();
  },

  // 初始化日期范围
  initDateRange() {
    const today = new Date();
    const maxDate = new Date();
    maxDate.setFullYear(today.getFullYear() + 10);

    this.setData({
      minDate: this.formatDate(today),
      maxDate: this.formatDate(maxDate)
    });
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = Session.getUser();
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    this.setData({ userInfo });
  },

  // 加载车辆信息
  async loadVehicleInfo() {
    const { userInfo } = this.data;
    
    try {
      this.setData({ loading: true });
      const vehicleInfo = await vehicleApi.getEmployeeVehicle(userInfo.id);
      
      if (vehicleInfo) {
        // 添加状态类名
        vehicleInfo.statusClass = this.getStatusClass(vehicleInfo.status);

        // 格式化日期显示
        const formData = {
          mileage: vehicleInfo.mileage ? vehicleInfo.mileage.toString() : '',
          appearance: vehicleInfo.appearance || '',
          insuranceExpiry: vehicleInfo.insuranceExpiry ? this.formatDate(vehicleInfo.insuranceExpiry) : '',
          licenseExpiry: vehicleInfo.licenseExpiry ? this.formatDate(vehicleInfo.licenseExpiry) : '',
          supplies: vehicleInfo.supplies || ''
        };

        this.setData({
          vehicleInfo,
          formData,
          originalFormData: { ...formData }
        });
      } else {
        wx.showToast({
          title: '未分配车辆',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('加载车辆信息失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 获取状态对应的CSS类名
  getStatusClass(status) {
    const statusMap = {
      '空闲': 'idle',
      '使用中': 'in-use',
      '维修中': 'maintenance',
      '停用': 'disabled'
    };
    return statusMap[status] || 'idle';
  },

  // 切换编辑模式
  toggleEdit() {
    const { editing } = this.data;
    if (editing) {
      // 取消编辑，恢复原始数据
      this.setData({
        editing: false,
        formData: { ...this.data.originalFormData }
      });
    } else {
      // 开始编辑
      this.setData({ editing: true });
    }
  },

  // 表单输入处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 保险到期日期选择
  onInsuranceDateChange(e) {
    const selectedDate = e.detail.value;
    this.setData({
      'formData.insuranceExpiry': selectedDate
    });
  },

  // 行驶证到期日期选择
  onLicenseDateChange(e) {
    const selectedDate = e.detail.value;
    this.setData({
      'formData.licenseExpiry': selectedDate
    });
  },

  // 验证表单数据
  validateForm() {
    const { formData } = this.data;
    
    // 里程数验证
    if (formData.mileage && (isNaN(formData.mileage) || parseFloat(formData.mileage) < 0 || parseFloat(formData.mileage) > 999999)) {
      wx.showToast({
        title: '里程数格式不正确',
        icon: 'none'
      });
      return false;
    }
    
    // 外观描述长度验证
    if (formData.appearance && formData.appearance.length > 1000) {
      wx.showToast({
        title: '外观描述不能超过1000字符',
        icon: 'none'
      });
      return false;
    }
    
    // 物资清单长度验证
    if (formData.supplies && formData.supplies.length > 2000) {
      wx.showToast({
        title: '物资清单不能超过2000字符',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  },

  // 保存车辆信息
  async saveVehicleInfo() {
    if (!this.validateForm()) {
      return;
    }
    
    const { userInfo, vehicleInfo, formData } = this.data;
    
    try {
      // 准备提交数据
      const submitData = {};
      
      if (formData.mileage) {
        submitData.mileage = parseFloat(formData.mileage);
      }
      if (formData.appearance) {
        submitData.appearance = formData.appearance;
      }
      if (formData.insuranceExpiry) {
        submitData.insuranceExpiry = formData.insuranceExpiry;
      }
      if (formData.licenseExpiry) {
        submitData.licenseExpiry = formData.licenseExpiry;
      }
      if (formData.supplies) {
        submitData.supplies = formData.supplies;
      }
      
      // 如果没有任何更改，直接退出编辑模式
      if (Object.keys(submitData).length === 0) {
        this.setData({ editing: false });
        return;
      }
      
      const result = await vehicleApi.updateVehicleInfo(vehicleInfo.id, userInfo.id, submitData);
      
      if (result) {
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        });
        
        // 更新本地数据
        this.setData({
          editing: false,
          originalFormData: { ...formData }
        });
        
        // 重新加载车辆信息
        this.loadVehicleInfo();
      }
    } catch (error) {
      console.error('保存车辆信息失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadVehicleInfo().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {}
});
