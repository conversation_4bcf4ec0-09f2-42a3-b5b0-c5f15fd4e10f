.all-additional-services {
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.service-item {
  border: 1rpx solid #e8e8e8;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.service-item:last-child {
  margin-bottom: 0;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.service-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.service-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.service-status.confirmed {
  background: #4CAF50;
}

.service-status.rejected {
  background: #f44336;
}

.service-status.pending_payment {
  background: #FF5722;
}

.service-status.paid {
  background: #34c759;
}

.service-status.completed {
  background: #2196F3;
}

.service-status.cancelled {
  background: #9E9E9E;
}

.service-status.refunding {
  background: #FF9800;
}

.service-status.refunded {
  background: #607D8B;
}

.service-customer-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.customer-label {
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
}

.customer-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-right: 8rpx;
}

.customer-phone {
  font-size: 24rpx;
  color: #999;
}

.service-details {
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
}

.detail-time {
  color: #999;
  font-size: 24rpx;
}

.service-price-info {
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
  background: #fff8f0;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff9500;
}

.price-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4rpx;
}

.price-row:last-child {
  margin-bottom: 0;
}

.price-label {
  font-size: 26rpx;
  color: #666;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.current-price {
  font-size: 28rpx;
  color: #ff9500;
  font-weight: bold;
}

.reject-reason {
  margin-top: 16rpx;
  padding: 12rpx 16rpx;
  background: #ffebee;
  border-radius: 8rpx;
  border-left: 4rpx solid #f44336;
}

.reason-label {
  font-size: 26rpx;
  color: #f44336;
  font-weight: bold;
  margin-right: 8rpx;
}

.reason-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.service-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
  margin-top: 20rpx;
}

.service-action-btn {
  padding: 12rpx 32rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  text-align: center;
  min-width: 120rpx;
  transition: all 0.2s ease;
}

.confirm-btn {
  background: rgba(76, 175, 80, 1);
  color: white;
}

.confirm-btn:active {
  background: rgba(76, 175, 80, 0.8);
  transform: scale(0.95);
}

.reject-btn {
  background: rgba(244, 67, 54, 1);
  color: white;
}

.reject-btn:active {
  background: rgba(244, 67, 54, 0.8);
  transform: scale(0.95);
}
