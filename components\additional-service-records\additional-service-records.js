Component({
  properties: {
    confirmedAdditionalServiceOrders: {
      type: Array,
      value: []
    }
  },

  data: {},

  methods: {
    // 确认追加服务（如果有待确认状态）
    onConfirmService(e) {
      const service = e.currentTarget.dataset.service;
      this.triggerEvent('confirmService', { service });
    },

    // 拒绝追加服务（如果有待确认状态）
    onRejectService(e) {
      const service = e.currentTarget.dataset.service;
      this.triggerEvent('rejectService', { service });
    }
  }
});
