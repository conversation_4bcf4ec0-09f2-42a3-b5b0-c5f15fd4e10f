.container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding: 20rpx;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}

.refresh-btn {
  padding: 12rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}

.refresh-icon {
  font-size: 28rpx;
  color: #fff;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 服务容器 */
.services-container {
  margin-bottom: 30rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 0 20rpx 20rpx 20rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.count-badge {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: #fff;
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-weight: bold;
  min-width: 40rpx;
  text-align: center;
}

.count-badge.available {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
}

/* 服务列表 */
.service-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.service-item {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.service-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 服务信息 */
.service-info {
  padding: 30rpx 30rpx 20rpx 30rpx;
  cursor: pointer;
  position: relative;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.service-name-section {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.service-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
}

.service-type {
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  align-self: flex-start;
  font-weight: 500;
}

.service-type.main {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: #fff;
}

.service-type.additional {
  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
  color: #fff;
}

.duration-display {
  text-align: right;
}

.duration-time {
  font-size: 36rpx;
  font-weight: bold;
  color: #1890ff;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  border: 2rpx solid #1890ff;
}

/* 订单信息 */
.order-info {
  display: flex;
  gap: 30rpx;
  margin-bottom: 16rpx;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.order-detail {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.order-label {
  font-size: 24rpx;
  color: #666;
}

.order-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.order-value.status {
  color: #1890ff;
  font-weight: bold;
}

/* 时间信息 */
.time-info {
  margin-bottom: 16rpx;
}

.time-detail {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.time-label {
  font-size: 24rpx;
  color: #666;
}

.time-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 服务详情 */
.service-detail {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.service-label {
  font-size: 24rpx;
  color: #666;
}

.service-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 备注信息 */
.remark-info {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
  margin-top: 8rpx;
  padding: 8rpx 12rpx;
  background: #f8f9fa;
  border-radius: 6rpx;
}

.remark-label {
  font-size: 22rpx;
  color: #666;
  flex-shrink: 0;
}

.remark-value {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
  word-break: break-all;
}

/* 点击提示 */
.click-hint {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  margin-top: 16rpx;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 12rpx;
  border: 1rpx solid #bbdefb;
}

.click-hint .hint-text {
  font-size: 22rpx;
  color: #1976d2;
  font-weight: 500;
}

.click-hint .hint-arrow {
  font-size: 24rpx;
  color: #1976d2;
  font-weight: bold;
}

/* 操作按钮 */
.service-actions {
  padding: 20rpx 30rpx 30rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
  background: #fafafa;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
}



.end-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.3);
}

.end-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

.btn-text {
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.empty-action .action-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

/* 温馨提示 */
.tips-container {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.tips-header {
  margin-bottom: 20rpx;
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tips-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 订单卡片样式 */
.order-cards {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.order-card {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
}

.order-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.order-card-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2rpx solid #dee2e6;
}

.order-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.order-sn {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.order-status {
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: #fff;
  font-weight: 500;
}

.customer-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.customer-name {
  font-size: 26rpx;
  color: #495057;
  font-weight: 500;
}

.customer-phone {
  font-size: 24rpx;
  color: #6c757d;
}

.services-overview {
  padding: 30rpx;
}

.service-summary {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.summary-label {
  font-size: 26rpx;
  color: #495057;
  font-weight: 500;
}

.summary-count {
  font-size: 24rpx;
  color: #00b894;
  font-weight: bold;
  background: rgba(0, 184, 148, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.service-preview {
  margin-bottom: 20rpx;
}

.preview-services {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.preview-item {
  font-size: 22rpx;
  color: #495057;
  background: #e3f2fd;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  border: 1rpx solid #bbdefb;
}

.preview-item.additional {
  background: #fce4ec;
  border-color: #f8bbd9;
  color: #ad1457;
}

.card-action-hint {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #f8f9fa;
  border-top: 1rpx solid #dee2e6;
}

.hint-text {
  font-size: 24rpx;
  color: #6c757d;
}

.hint-arrow {
  font-size: 28rpx;
  color: #00b894;
  font-weight: bold;
}
