/* pages/mine/vehicle/index.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.vehicle-container {
  padding: 20rpx;
}

.info-card {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.card-actions {
  display: flex;
  gap: 20rpx;
}

.btn-edit {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
}

.edit-actions {
  display: flex;
  gap: 20rpx;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
}

.btn-save {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
}

.info-row, .form-row {
  display: flex;
  margin-bottom: 20rpx;
  align-items: flex-start;
}

.label, .form-label {
  width: 200rpx;
  color: #666;
  font-size: 28rpx;
  flex-shrink: 0;
  line-height: 1.5;
}

.value {
  flex: 1;
  color: #333;
  font-size: 28rpx;
  line-height: 1.5;
  word-break: break-all;
}

.form-value {
  flex: 1;
}

.form-input {
  width: 100%;
  padding: 16rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.date-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  background-color: #fff;
}

.date-text {
  font-size: 28rpx;
  color: #333;
}

.date-text.placeholder {
  color: #999;
}

.icon-arrow {
  color: #999;
  font-size: 24rpx;
}

.status-空闲 {
  color: #52c41a;
}

.status-使用中 {
  color: #1890ff;
}

.status-维修中 {
  color: #faad14;
}

.status-停用 {
  color: #f5222d;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .info-row, .form-row {
    flex-direction: column;
  }
  
  .label, .form-label {
    width: 100%;
    margin-bottom: 10rpx;
  }
}
