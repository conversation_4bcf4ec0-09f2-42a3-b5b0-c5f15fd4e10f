/* pages/mine/vehicle/index.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.vehicle-container {
  padding: 20rpx;
}

.info-card {
  background-color: white;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.card-actions {
  display: flex;
  gap: 20rpx;
}

.btn-edit {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
  outline: none;
  box-sizing: border-box;
}

.btn-edit::after {
  border: none;
}

.btn-edit:active {
  background-color: #0056cc;
  transform: scale(0.98);
}

.edit-actions {
  display: flex;
  gap: 20rpx;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
  outline: none;
  box-sizing: border-box;
}

.btn-cancel::after {
  border: none;
}

.btn-cancel:active {
  background-color: #e0e0e0;
  transform: scale(0.98);
}

.btn-save {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
  outline: none;
  box-sizing: border-box;
}

.btn-save::after {
  border: none;
}

.btn-save:active {
  background-color: #0056cc;
  transform: scale(0.98);
}

.info-row, .form-row {
  display: flex;
  flex-direction: column;
  margin-bottom: 30rpx;
}

.label, .form-label {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 12rpx;
  line-height: 1.5;
}

.value {
  color: #666;
  font-size: 28rpx;
  line-height: 1.5;
  word-break: break-all;
  min-height: 40rpx;
  padding: 12rpx 0;
}

.form-value {
  width: 100%;
}

.form-input {
  width: 100%;
  height: 2.4rem;
  padding: 0 16rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fafafa;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #007aff;
  background-color: #fff;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx 16rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fafafa;
  box-sizing: border-box;
  transition: all 0.3s ease;
  resize: none;
}

.form-textarea:focus {
  border-color: #007aff;
  background-color: #fff;
}

.date-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 16rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  background-color: #fafafa;
  transition: all 0.3s ease;
  min-height: 40rpx;
}

.date-picker:active {
  background-color: #f0f0f0;
}

.date-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.date-text.placeholder {
  color: #999;
}

.icon-arrow {
  color: #999;
  font-size: 24rpx;
  margin-left: 16rpx;
}

.status-idle {
  color: #52c41a;
}

.status-in-use {
  color: #1890ff;
}

.status-maintenance {
  color: #faad14;
}

.status-disabled {
  color: #f5222d;
}

/* 表单聚焦效果 */
.form-input::placeholder,
.form-textarea::placeholder {
  color: #999;
}

/* 卡片阴影优化 */
.info-card:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}
