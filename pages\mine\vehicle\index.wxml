<view class="container">
  <diy-navbar :isFixed="true" bgColor="white" CustomBar='60'>
    <view slot="content">
      <view class="flex align-center flex-nowrap justify-start diygw-col-24 title-left">
        车辆管理
      </view>
    </view>
  </diy-navbar>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 车辆信息 -->
  <view wx:else class="vehicle-container">
    <!-- 车辆基本信息 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">车辆基本信息</text>
      </view>
      <view class="info-row">
        <text class="label">车牌号：</text>
        <text class="value">{{vehicleInfo.plateNumber}}</text>
      </view>
      <view class="info-row">
        <text class="label">车辆类型：</text>
        <text class="value">{{vehicleInfo.vehicleType}}</text>
      </view>
      <view class="info-row">
        <text class="label">车辆状态：</text>
        <text class="value status-{{vehicleInfo.statusClass}}">{{vehicleInfo.status}}</text>
      </view>
    </view>

    <!-- 车辆详细信息 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">车辆详细信息</text>
        <view class="card-actions">
          <button wx:if="{{!editing}}" class="btn-edit" bindtap="toggleEdit">编辑</button>
          <view wx:else class="edit-actions">
            <button class="btn-cancel" bindtap="toggleEdit">取消</button>
            <button class="btn-save" bindtap="saveVehicleInfo">保存</button>
          </view>
        </view>
      </view>

      <!-- 里程数 -->
      <view class="form-row">
        <text class="form-label">里程数（公里）：</text>
        <view class="form-value">
          <input wx:if="{{editing}}" 
                 class="form-input" 
                 type="digit" 
                 placeholder="请输入里程数" 
                 value="{{formData.mileage}}"
                 data-field="mileage"
                 bindinput="onInputChange" />
          <text wx:else class="value">{{vehicleInfo.mileage || '未填写'}}</text>
        </view>
      </view>

      <!-- 外观描述 -->
      <view class="form-row">
        <text class="form-label">外观描述：</text>
        <view class="form-value">
          <textarea wx:if="{{editing}}" 
                    class="form-textarea" 
                    placeholder="请描述车辆外观状况" 
                    value="{{formData.appearance}}"
                    data-field="appearance"
                    bindinput="onInputChange"
                    maxlength="1000" />
          <text wx:else class="value">{{vehicleInfo.appearance || '未填写'}}</text>
        </view>
      </view>

      <!-- 保险到期时间 -->
      <view class="form-row">
        <text class="form-label">保险到期时间：</text>
        <view class="form-value">
          <picker wx:if="{{editing}}"
                  mode="date"
                  value="{{formData.insuranceExpiry}}"
                  start="{{minDate}}"
                  end="{{maxDate}}"
                  bindchange="onInsuranceDateChange">
            <view class="date-picker">
              <text class="date-text {{formData.insuranceExpiry ? '' : 'placeholder'}}">
                {{formData.insuranceExpiry || '请选择日期'}}
              </text>
              <text class="icon-arrow">></text>
            </view>
          </picker>
          <text wx:else class="value">{{vehicleInfo.insuranceExpiry ? formData.insuranceExpiry : '未填写'}}</text>
        </view>
      </view>

      <!-- 行驶证到期时间 -->
      <view class="form-row">
        <text class="form-label">行驶证到期时间：</text>
        <view class="form-value">
          <picker wx:if="{{editing}}"
                  mode="date"
                  value="{{formData.licenseExpiry}}"
                  start="{{minDate}}"
                  end="{{maxDate}}"
                  bindchange="onLicenseDateChange">
            <view class="date-picker">
              <text class="date-text {{formData.licenseExpiry ? '' : 'placeholder'}}">
                {{formData.licenseExpiry || '请选择日期'}}
              </text>
              <text class="icon-arrow">></text>
            </view>
          </picker>
          <text wx:else class="value">{{vehicleInfo.licenseExpiry ? formData.licenseExpiry : '未填写'}}</text>
        </view>
      </view>

      <!-- 物资清单 -->
      <view class="form-row">
        <text class="form-label">物资清单：</text>
        <view class="form-value">
          <textarea wx:if="{{editing}}" 
                    class="form-textarea" 
                    placeholder="请列出车辆配备的物资清单" 
                    value="{{formData.supplies}}"
                    data-field="supplies"
                    bindinput="onInputChange"
                    maxlength="2000" />
          <text wx:else class="value">{{vehicleInfo.supplies || '未填写'}}</text>
        </view>
      </view>
    </view>

    <!-- 更新记录 -->
    <view wx:if="{{vehicleInfo.lastSubmittedAt}}" class="info-card">
      <view class="card-header">
        <text class="card-title">最后更新记录</text>
      </view>
      <view class="info-row">
        <text class="label">更新时间：</text>
        <text class="value">{{vehicleInfo.lastSubmittedAt}}</text>
      </view>
      <view wx:if="{{vehicleInfo.lastSubmittedEmployee}}" class="info-row">
        <text class="label">更新人：</text>
        <text class="value">{{vehicleInfo.lastSubmittedEmployee.name}} ({{vehicleInfo.lastSubmittedEmployee.phone}})</text>
      </view>
    </view>
  </view>


</view>
